# @generated by tools/pyi/gen_pyi.py from torch/_C/return_types.pyi.in
# mypy: allow-untyped-defs

from typing import (
    Any,
    Callable,
    ContextManager,
    Iterator,
    List,
    Literal,
    NamedTuple,
    NoReturn,
    Optional,
    overload,
    Sequence,
    Tuple,
    Type,
    TypeVar,
    Union,
)

from torch import contiguous_format, Generator, inf, memory_format, strided, Tensor, SymInt
from torch.types import (
    _bool,
    _device,
    _dtype,
    _float,
    _int,
    _layout,
    _qscheme,
    _size,
    Number,
)

class _fake_quantize_per_tensor_affine_cachemask_tensor_qparams(Tuple[Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def mask(self) -> Tensor: ...
    def __new__(cls, sequence: Tu<PERSON>[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _fused_moving_avg_obs_fq_helper(<PERSON><PERSON>[Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def mask(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_det(Tuple[Tensor, Tensor, Tensor]):
    @property
    def result(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor]): ...
    n_fields: _int = 3
    n_sequeunce_fields: _int = 3
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_eigh(Tuple[Tensor, Tensor]):
    @property
    def eigenvalues(self) -> Tensor: ...
    @property
    def eigenvectors(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_slogdet(Tuple[Tensor, Tensor, Tensor, Tensor]):
    @property
    def sign(self) -> Tensor: ...
    @property
    def logabsdet(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor, Tensor]): ...
    n_fields: _int = 4
    n_sequeunce_fields: _int = 4
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_solve_ex(Tuple[Tensor, Tensor, Tensor, Tensor]):
    @property
    def result(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    @property
    def info(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor, Tensor]): ...
    n_fields: _int = 4
    n_sequeunce_fields: _int = 4
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_svd(Tuple[Tensor, Tensor, Tensor]):
    @property
    def U(self) -> Tensor: ...
    @property
    def S(self) -> Tensor: ...
    @property
    def Vh(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor]): ...
    n_fields: _int = 3
    n_sequeunce_fields: _int = 3
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _lu_with_info(Tuple[Tensor, Tensor, Tensor]):
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    @property
    def info(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor]): ...
    n_fields: _int = 3
    n_sequeunce_fields: _int = 3
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_cudnn_attention(Tuple[Tensor, Tensor, Tensor, Tensor, Union[_int, SymInt], Union[_int, SymInt], Tensor, Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    @property
    def cum_seq_q(self) -> Tensor: ...
    @property
    def cum_seq_k(self) -> Tensor: ...
    @property
    def max_q(self) -> Union[_int, SymInt]: ...
    @property
    def max_k(self) -> Union[_int, SymInt]: ...
    @property
    def philox_seed(self) -> Tensor: ...
    @property
    def philox_offset(self) -> Tensor: ...
    @property
    def debug_attn_mask(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor, Tensor, Union[_int, SymInt], Union[_int, SymInt], Tensor, Tensor, Tensor]): ...
    n_fields: _int = 9
    n_sequeunce_fields: _int = 9
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_efficient_attention(Tuple[Tensor, Tensor, Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def log_sumexp(self) -> Tensor: ...
    @property
    def philox_seed(self) -> Tensor: ...
    @property
    def philox_offset(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor, Tensor]): ...
    n_fields: _int = 4
    n_sequeunce_fields: _int = 4
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_flash_attention(Tuple[Tensor, Tensor, Tensor, Tensor, Union[_int, SymInt], Union[_int, SymInt], Tensor, Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    @property
    def cum_seq_q(self) -> Tensor: ...
    @property
    def cum_seq_k(self) -> Tensor: ...
    @property
    def max_q(self) -> Union[_int, SymInt]: ...
    @property
    def max_k(self) -> Union[_int, SymInt]: ...
    @property
    def philox_seed(self) -> Tensor: ...
    @property
    def philox_offset(self) -> Tensor: ...
    @property
    def debug_attn_mask(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor, Tensor, Union[_int, SymInt], Union[_int, SymInt], Tensor, Tensor, Tensor]): ...
    n_fields: _int = 9
    n_sequeunce_fields: _int = 9
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_flash_attention_for_cpu(Tuple[Tensor, Tensor]):
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _unpack_dual(Tuple[Tensor, Tensor]):
    @property
    def primal(self) -> Tensor: ...
    @property
    def tangent(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class aminmax(Tuple[Tensor, Tensor]):
    @property
    def min(self) -> Tensor: ...
    @property
    def max(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class cummax(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class cummin(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class frexp(Tuple[Tensor, Tensor]):
    @property
    def mantissa(self) -> Tensor: ...
    @property
    def exponent(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class geqrf(Tuple[Tensor, Tensor]):
    @property
    def a(self) -> Tensor: ...
    @property
    def tau(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class histogram(Tuple[Tensor, Tensor]):
    @property
    def hist(self) -> Tensor: ...
    @property
    def bin_edges(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class histogramdd(Tuple[Tensor, Tuple[Tensor, ...]]):
    @property
    def hist(self) -> Tensor: ...
    @property
    def bin_edges(self) -> Tuple[Tensor, ...]: ...
    def __new__(cls, sequence: Tuple[Tensor, Tuple[Tensor, ...]]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class kthvalue(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class lu_unpack(Tuple[Tensor, Tensor, Tensor]):
    @property
    def P(self) -> Tensor: ...
    @property
    def L(self) -> Tensor: ...
    @property
    def U(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor]): ...
    n_fields: _int = 3
    n_sequeunce_fields: _int = 3
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class max(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class median(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class min(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class mode(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class nanmedian(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class qr(Tuple[Tensor, Tensor]):
    @property
    def Q(self) -> Tensor: ...
    @property
    def R(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class slogdet(Tuple[Tensor, Tensor]):
    @property
    def sign(self) -> Tensor: ...
    @property
    def logabsdet(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class sort(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class svd(Tuple[Tensor, Tensor, Tensor]):
    @property
    def U(self) -> Tensor: ...
    @property
    def S(self) -> Tensor: ...
    @property
    def V(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor, Tensor]): ...
    n_fields: _int = 3
    n_sequeunce_fields: _int = 3
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class topk(Tuple[Tensor, Tensor]):
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class triangular_solve(Tuple[Tensor, Tensor]):
    @property
    def solution(self) -> Tensor: ...
    @property
    def cloned_coefficient(self) -> Tensor: ...
    def __new__(cls, sequence: Tuple[Tensor, Tensor]): ...
    n_fields: _int = 2
    n_sequeunce_fields: _int = 2
    n_unnamed_fields: _int = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

all_return_types: List[Type] = []
